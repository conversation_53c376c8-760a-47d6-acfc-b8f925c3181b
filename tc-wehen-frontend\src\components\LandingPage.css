/* TC-<PERSON><PERSON> Landing Page Styles */

.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--gradient-light);
  position: relative;
}

.landing-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(234, 88, 12, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Modern Header Styles */
.header {
  background: var(--gradient-primary);
  color: var(--white);
  padding: 1.25rem 0;
  box-shadow: var(--shadow-xl);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-visible {
  transform: translateY(0);
  opacity: 1;
}

.header-hidden {
  transform: translateY(-100%);
  opacity: 0.95;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 2rem;
  gap: 3rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  color: var(--white);
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.02);
}

.logo-img {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.logo-img:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: rotate(5deg);
}

.logo h1 {
  font-size: 2.4rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--white) 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  cursor: pointer;
  padding: 4px;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.mobile-menu-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.hamburger-line {
  width: 20px;
  height: 2.5px;
  background-color: var(--white);
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Modern Navigation Styles */
.nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
}

/* Mobile Navigation - Hidden by default */
.mobile-nav {
  display: none;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 1rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-list a {
  color: var(--white);
  text-decoration: none;
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
  backdrop-filter: blur(10px);
}

.nav-list a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-list a:hover::before,
.nav-list a.active::before {
  opacity: 1;
}

.nav-list a:hover,
.nav-list a.active {
  transform: none;
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-arrow {
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dropdown-open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  backdrop-filter: blur(10px);
  border: 2px solid var(--gray-300);
  min-width: 240px;
  width: max-content;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-3px);
  transition: all 0.2s ease;
  z-index: 1000;
  overflow: hidden;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Desktop dropdown - only show on hover, not by default */
@media (min-width: 769px) {
  .dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-3px);
  }

  .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.dropdown-menu li {
  list-style: none;
}

.dropdown-menu a {
  color: var(--gray-900) !important;
  display: block;
  padding: 1rem 1.5rem;
  border-radius: 0;
  font-weight: 600;
  font-size: 0.95rem;
  text-align: center;
  line-height: 1.4;
  text-decoration: none;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--gray-200);
}

.dropdown-menu a:last-child {
  border-bottom: none;
}

.dropdown-menu a:hover {
  background-color: var(--primary-red);
  color: var(--primary-white) !important;
  transform: none;
  font-weight: 600;
}

/* Modern Booking Button Styling */
.booking-button {
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%) !important;
  font-weight: 700 !important;
  box-shadow: var(--shadow-lg) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: var(--radius-xl) !important;
  padding: 0.875rem 1.75rem !important;
  letter-spacing: 0.025em !important;
  position: relative !important;
  overflow: hidden !important;
}

.booking-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.booking-button:hover::before {
  left: 100%;
}

.booking-button:hover {
  background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%) !important;
  box-shadow: var(--shadow-2xl) !important;
  transform: translateY(-1px) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* Main Content */
.main {
  flex: 1;
  padding-top: 80px; /* Account for fixed header */
}

/* Modern Page Layout */
.page {
  min-height: calc(100vh - 90px);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  margin-top: 90px;
}

.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
  backdrop-filter: blur(2px);
}

/* Modern Content Container */
.content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 1rem auto;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.98);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.content:hover {
  transform: translateY(-1px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
}

/* Modern Home Page */
.home-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.welcome-message {
  text-align: center;
  background: rgba(255, 255, 255, 0.98);
  padding: 4rem 3rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--gradient-primary);
}

.welcome-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.2);
}

.welcome-message h2 {
  font-size: 3.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.welcome-subtitle {
  font-size: 1.3rem;
  color: var(--gray-600);
  margin-bottom: 0;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Modern News Section */
.news-section {
  background: rgba(255, 255, 255, 0.98);
  padding: 3rem;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.news-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
}

.news-section h3 {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.025em;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.news-item {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.news-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-tennis);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.news-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.news-item:hover::before {
  transform: scaleX(1);
}

.news-date {
  color: var(--logo-blue);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.news-item h4 {
  color: var(--gray-900);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

.news-item p {
  color: var(--gray-700);
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
}

/* Training Page */
.training-content {
  text-align: center;
}

.training-logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 2rem;
}

/* Verein Page */
.verein-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.verein-content p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.8;
}

/* Anlage Page */
.anlage-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.anlage-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.anlage-images img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow);
  transition: transform 0.3s ease;
}

.anlage-images img:hover {
  transform: scale(1.05);
}

/* Kontakt Page */
.kontakt-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.kontakt-info {
  margin-bottom: 2rem;
}

.kontakt-info h3 {
  color: var(--primary-red);
  margin-bottom: 1rem;
}

.kontakt-info p {
  margin-bottom: 0.5rem;
}

.maps-container {
  margin-top: 2rem;
}

/* Impressum Page */
.impressum-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.impressum-info h3 {
  color: var(--logo-blue);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.impressum-info p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Datenschutz Page */
.datenschutz-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.datenschutz-info h3 {
  color: var(--logo-blue);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.datenschutz-info h4 {
  color: var(--primary-orange);
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
}

.datenschutz-info p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.datenschutz-info ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.datenschutz-info li {
  margin-bottom: 0.5rem;
}

/* Placeholder Page */
.placeholder-content h2 {
  color: var(--primary-red);
  margin-bottom: 2rem;
  text-align: center;
}

.placeholder-info {
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.8;
}

.placeholder-info p {
  margin-bottom: 1.5rem;
}

/* Modern Back Button Styles */
.back-button-container {
  margin-bottom: 2rem;
  text-align: left;
}

.back-button {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: 1rem 1.75rem;
  border-radius: var(--radius-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-lg);
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.back-button:hover::before {
  left: 100%;
}

.back-button:hover {
  background: var(--gradient-accent);
  transform: translateY(-3px);
  box-shadow: var(--shadow-2xl);
}

.back-button:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Modern Footer Styles */
.footer {
  background: var(--gradient-primary);
  color: var(--white);
  padding: 3rem 0 2rem;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.footer-links {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--white);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.025em;
}

.footer-links a:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.footer-links span {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
}

.footer-text p {
  margin: 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  letter-spacing: 0.025em;
}

/* Mobile Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 64px;
  height: 64px;
  background: var(--gradient-tennis);
  border: none;
  border-radius: 50%;
  box-shadow: var(--shadow-2xl);
  cursor: pointer;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  color: var(--white);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.floating-action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 20px 40px rgba(22, 163, 74, 0.4);
  background: linear-gradient(135deg, var(--logo-green) 0%, var(--tennis-green) 100%);
}

.floating-action-btn:active {
  transform: scale(0.95);
}

.fab-text {
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Hide FAB on desktop - only show on mobile/tablet */
@media (min-width: 1024px) {
  .floating-action-btn {
    display: none;
  }
}

/* Adjust FAB size for smaller screens */
@media (max-width: 480px) {
  .floating-action-btn {
    width: 56px;
    height: 56px;
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .fab-text {
    font-size: 0.65rem;
  }
}

/* Touch and Mobile Improvements */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Improved Touch Targets for Mobile */
@media (max-width: 768px) {
  .nav-list a {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .booking-button {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-height: 52px !important;
  }

  .mobile-menu-toggle {
    min-width: 48px;
    min-height: 48px;
  }

  /* Better mobile content spacing */
  .content {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
  }

  .welcome-message {
    padding: 2.5rem 2rem;
  }

  .news-section {
    padding: 2rem 1.5rem;
  }

  .news-item {
    padding: 1.5rem;
  }

  /* Improved mobile forms */
  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 48px;
  }

  /* Better mobile buttons */
  .btn {
    min-height: 48px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .back-button {
    min-height: 48px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .header-content {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.8rem;
  }

  .welcome-message h2 {
    font-size: 2.5rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .content {
    padding: 1.5rem 1rem;
    margin: 0.25rem;
  }

  .news-grid {
    gap: 1rem;
  }

  .news-item {
    padding: 1.25rem;
  }
}

/* Landscape mobile optimization */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-message {
    padding: 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .floating-action-btn {
    width: 52px;
    height: 52px;
    bottom: 1rem;
    right: 1rem;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  /* Add padding-top to body to account for fixed header */
  body {
    padding-top: 80px;
  }

  .header-content {
    justify-content: space-between;
    padding: 0.5rem 1rem;
    gap: 1rem;
  }

  .logo {
    gap: 0.8rem;
  }

  .logo-img {
    height: 45px;
    width: 45px;
  }

  .logo h1 {
    font-size: 1.8rem;
  }

  /* Hide desktop navigation on mobile - force with !important */
  .desktop-nav {
    display: none !important;
  }

  /* Hide all desktop navigation elements */
  .desktop-nav .nav-list {
    display: none !important;
  }

  /* Show hamburger menu on mobile */
  .mobile-menu-toggle {
    display: flex !important;
  }

  /* Show mobile navigation on mobile */
  .mobile-nav {
    display: block;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg,
      rgba(220, 53, 69, 0.95) 0%,
      rgba(255, 107, 53, 0.95) 50%,
      rgba(52, 144, 220, 0.95) 100%);
    backdrop-filter: blur(10px);
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }

  /* Show navigation when open */
  .mobile-nav.nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
    margin: 0;
    width: 100%;
  }

  .nav-list li {
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-list li:last-child {
    border-bottom: none;
  }

  .nav-list a {
    display: block;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    text-align: left;
    border-radius: 0;
    transition: background-color 0.3s ease;
  }

  .nav-list a:hover,
  .nav-list a.active {
    background-color: rgba(255, 255, 255, 0.25);
    transform: none;
  }

  .booking-button {
    background: linear-gradient(135deg, #ef4444 0%, #f97316 100%) !important;
    font-weight: bold !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
  }

  .booking-button:hover {
    background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%) !important;
  }



  /* Mobile dropdown - keep collapsed by default */
  .dropdown-menu {
    position: static;
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    background: var(--white);
    backdrop-filter: blur(15px);
    border: 2px solid var(--gray-300);
    border-radius: 12px;
    margin: 0.5rem 0;
    transition: all 0.2s ease;
  }

  /* Show dropdown on click (mobile) */
  .dropdown-open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    max-height: 300px;
  }

  .dropdown-menu a {
    padding: 1rem 2rem !important;
    padding-left: 3rem !important;
    font-size: 1rem !important;
    color: var(--gray-900) !important;
    font-weight: 600 !important;
    text-align: left !important;
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.2s ease !important;
  }

  .dropdown-menu a:last-child {
    border-bottom: none;
  }

  .dropdown-menu a:hover {
    background-color: var(--primary-red) !important;
    color: var(--primary-white) !important;
    font-weight: 600 !important;
  }

  /* Mobile Home Page */
  .home-content {
    padding: 1rem;
    gap: 1.5rem;
  }

  .welcome-message {
    padding: 2rem 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .news-section h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .news-item {
    padding: 1.2rem;
  }

  .news-item h4 {
    font-size: 1.1rem;
  }

  .content {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 15px;
  }

  .anlage-images {
    grid-template-columns: 1fr;
    gap: 1rem;
  }



  /* Mobile Footer */
  .footer-content {
    padding: 0 1rem;
  }

  .footer-links {
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .footer-text p {
    font-size: 0.8rem;
  }

  /* Mobile background optimization - lighter */
  .page {
    background-attachment: scroll;
    min-height: auto;
    padding: 2rem 0;
  }

  .page::before {
    background: rgba(0, 0, 0, 0.3); /* Heller gemacht */
  }

  /* Mobile content areas lighter */
  .content {
    background: rgba(255, 255, 255, 0.95) !important; /* Heller */
    backdrop-filter: blur(10px);
  }

  /* Mobile back button */
  .back-button {
    font-size: 0.9rem;
    padding: 0.7rem 1.2rem;
    width: 100%;
    justify-content: center;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.4rem;
  }

  /* Mobile navigation styles for smaller screens */
  .mobile-nav .nav-list a {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .mobile-nav .booking-button {
    font-size: 1rem !important;
    padding: 1rem 2rem !important;
  }

  .welcome-message {
    padding: 2rem 1rem;
  }

  .welcome-message h2 {
    font-size: 1.5rem;
  }
}
